package notice

import (
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AppNotificationTypeHandler interface {
	Lists(c *gin.Context)
	Get(c *gin.Context)
	Detail(c *gin.Context)
	Create(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type appNotificationTypeHandler struct {
	svc service.AppNotificationTypeSvc
}

func NewAppNotificationTypeHandler(svc service.AppNotificationTypeSvc) AppNotificationTypeHandler {
	return &appNotificationTypeHandler{
		svc: svc,
	}
}

// Lists 获取推送消息类型列表
func (h *appNotificationTypeHandler) Lists(c *gin.Context) {
	list, err := h.svc.GetAppNotificationTypeList(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 转换响应格式
	var responseList []map[string]interface{}
	for _, item := range list {
		msgTypeText := "文本消息"
		if item.MsgType == "image" {
			msgTypeText = "图文消息"
		}

		actionText := "无"
		switch item.Action {
		case "forward":
			actionText = "跳转"
		case "check_app_upgrade":
			actionText = "检查app更新"
		}

		popupText := "否"
		if item.Popup == 1 {
			popupText = "是"
		}

		bannerText := "否"
		if item.Banner == 1 {
			bannerText = "是"
		}

		responseList = append(responseList, map[string]interface{}{
			"id":         item.ID,
			"name":       item.Name,
			"icon":       utils.AddPrefix(item.Icon),
			"slug":       item.Slug,
			"msg_type":   msgTypeText,
			"action":     actionText,
			"popup":      popupText,
			"banner":     bannerText,
			"created_at": item.CreatedAt,
			"updated_at": item.UpdatedAt,
		})
	}

	handler.Success(c, gin.H{
		"data": responseList,
	})
}

// Get 获取指定推送消息类型
func (h *appNotificationTypeHandler) Get(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	notificationType, err := h.svc.GetAppNotificationTypeByID(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if notificationType == nil {
		handler.Error(c, errors.NewErr("消息类型不存在"))
		return
	}
	notificationType.Icon = utils.AddPrefix(notificationType.Icon)

	handler.Success(c, gin.H{
		"data": notificationType,
	})
}

// Detail 根据slug获取推送消息类型详情
func (h *appNotificationTypeHandler) Detail(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		handler.Error(c, errors.NewErr("标识不能为空"))
		return
	}

	detail, err := h.svc.GetAppNotificationTypeDetail(c, slug)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data": detail,
	})
}

// Create 创建推送消息类型
func (h *appNotificationTypeHandler) Create(c *gin.Context) {
	var req service.CreateAppNotificationTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id, err := h.svc.CreateAppNotificationType(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"id": id,
	})
}

// Update 更新推送消息类型
func (h *appNotificationTypeHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	var req service.UpdateAppNotificationTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	err = h.svc.UpdateAppNotificationType(c, id, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// Delete 删除推送消息类型
func (h *appNotificationTypeHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	err = h.svc.DeleteAppNotificationType(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}
