package dao

import (
	"database/sql"
	"marketing/internal/api/action"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/upload"
	"marketing/internal/pkg/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ActionDao interface {
	//操作
	CreateActivity(c *gin.Context, actions model.Actions) (id int, err error)
	DeleteAction(ctx *gin.Context, id int, uid int) error
	UpdateAction(ctx *gin.Context, uid uint, actions model.Actions) (int, error)
	FallbackAction(c *gin.Context, u uint, status uint8) error
	UpdateActionFileds(c *gin.Context, action model.Actions, status ...uint8) error
	UpdateActionFieldsWithoutHooks(c *gin.Context, action model.Actions, statuses ...uint8) error
	//查询
	SearchActionList(ctx *gin.Context, params action.SearchActionParams) ([]action.InfoAction, int64, int, int, error)
	GetActionInfo(c *gin.Context, id int, uid ...uint) (model.Actions, error)
	GetActionApplyInfo(c *gin.Context, id, uid int) (model.ApplyInfoActions, error)
	GetActionFinishInfo(c *gin.Context, id, uid int) (model.FinishInfoAction, error)
	GetActionAuditInfo(c *gin.Context, id, uid int) (action.AuditInfoAction, error)
	GetActionVerifyInfo(c *gin.Context, id, uid int) (action.VerifyInfoAction, error)
	//提示
	GetActionStatus(c *gin.Context, id uint) (uint8, error)
	GetActionModel(c *gin.Context, id int) (model.ActionJoin, error)
	GetActionHadApply(ctx *gin.Context, id uint, tp int) int
	GetWarrantyCard(ctx *gin.Context, id uint) (total int, count int, err error)
	GetActionRecordedInfo(c *gin.Context, id, uid int) (map[string]any, error)
	AccordWarranty(c *gin.Context, id uint) ([]model.ActionSalesList, error)
	CreateSalesList(c *gin.Context, warranty []model.ActionSalesList) error
	GetActionFiled(c *gin.Context, id int, filed ...string) (map[string]any, error)
	GetActionCard(c *gin.Context, id int, tp int) (action.EndpointCard[time.Time], error)
}

type ActionGorm struct {
	db   *gorm.DB
	file upload.FileLoadService
}

func (a *ActionGorm) GetActionInfo(c *gin.Context, id int, uid ...uint) (model.Actions, error) {
	var result model.Actions
	row := a.db.WithContext(c).Table("actions").Where("id = ?", id)
	if len(uid) > 0 {
		row = row.Where("uid = ?", uid[0])
	}
	return result, nil
}

func (a *ActionGorm) UpdateActionFileds(c *gin.Context, action model.Actions, status ...uint8) error {
	// 检查statuses是否为空
	if len(status) == 0 {
		return myErrors.NewErr("状态参数不能为空")
	}
	row := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ? AND status IN (?)", action.ID, status).Updates(&action)
	if row.Error != nil {
		return row.Error
	}
	if row.RowsAffected == 0 {
		return myErrors.NewErr("更新失败")
	}
	return nil
}
func (a *ActionGorm) UpdateActionFieldsWithoutHooks(c *gin.Context, action model.Actions, statuses ...uint8) error {
	// 检查statuses是否为空
	if len(statuses) == 0 {
		return myErrors.NewErr("状态参数不能为空")
	}

	// 更新操作，跳过钩子函数
	row := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ? AND status IN (?)", action.ID, statuses).UpdateColumns(&action)
	if row.Error != nil {
		return row.Error
	}

	// 检查是否更新了至少一行
	if row.RowsAffected == 0 {
		return myErrors.NewErr("更新失败：没有找到匹配的记录")
	}

	return nil
}

func (a *ActionGorm) GetActionCard(c *gin.Context, id int, tp int) (action.EndpointCard[time.Time], error) {
	var result action.EndpointCard[time.Time]
	err := a.db.WithContext(c).Table("endpoint").
		Joins("LEFT JOIN warranty ON endpoint.id=warranty.endpoint").
		Joins("LEFT JOIN actions ON actions.endpoint_id=endpoint.id AND actions.type=?", tp).
		Where("endpoint.id=?", id).
		Select("endpoint.created_at,max(warranty.updated_at) as updated_at," +
			"count(DISTINCT warranty.id) as count," + "max(actions.created_at) as near_time").
		Find(&result).Error
	return result, err
}

func (a *ActionGorm) GetActionFiled(c *gin.Context, id int, filed ...string) (map[string]any, error) {
	result := make(map[string]any)

	// 构建查询
	query := a.db.WithContext(c).Table("actions").Where("id = ?", id)

	// 处理字段选择
	if len(filed) > 0 {
		// 将字段列表转换为逗号分隔的字符串
		fields := strings.Join(filed, ",")
		query = query.Select(fields)
	} else {
		query = query.Select("*")
	}

	// 执行查询
	err := query.Find(&result).Error
	if err != nil {
		return nil, err
	}

	// 处理时间字段的格式化
	if val, ok := result["created_at"].(time.Time); ok {
		result["created_at"] = val.Format("2006-01-02 15:04:05")
	}
	if val, ok := result["updated_at"].(time.Time); ok {
		result["updated_at"] = val.Format("2006-01-02 15:04:05")
	}

	return result, nil
}

// CreateSalesList 批量同步名单
func (a *ActionGorm) CreateSalesList(c *gin.Context, warranty []model.ActionSalesList) error {
	return a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		return tx.CreateInBatches(&warranty, 100).Error
	})
}

// AccordWarranty 筛选符合条件的活动保卡
func (a *ActionGorm) AccordWarranty(c *gin.Context, id uint) ([]model.ActionSalesList, error) {
	var act []model.ActionSalesList
	curDB := a.db.WithContext(c).
		Table("actions AS a").
		Select("w.id AS warranty_id, buy_date,a.id AS action_id").
		Joins("JOIN warranty AS w ON a.endpoint_id = w.endpoint AND w.status = 1 "+
			"AND w.buy_date BETWEEN a.date_start AND a.date_end").
		Where("a.id = ?", id).
		Find(&act)
	return act, curDB.Error
}

func (a *ActionGorm) GetActionRecordedInfo(c *gin.Context, id, uid int) (map[string]any, error) {
	var act map[string]any
	curDB := a.db.WithContext(c).Table("actions").
		Select("account_user_id as id,account_opinion as opinion,account_time as time,support_money as money").
		Where("id = ?", id).
		Where("status = ?", action.ActionStatusEntrySuccess)
	if uid != 0 {
		curDB = curDB.Where("uid = ?", uid)
	}
	err := curDB.Find(&act).Error
	if t, ok := act["time"].(time.Time); ok {
		act["time"] = t.Format("2006-01-02 15:04:05")
	}
	return act, err
}

// FallbackAction 回退操作（2 3 4 5）
func (a *ActionGorm) FallbackAction(c *gin.Context, u uint, status uint8) error {
	condition := action.Status(status)
	//已审核回退
	if condition == action.ActionStatusApprovedByLeader || condition == action.ActionStatusApproved {
		var AuditAction struct {
			action.AuditAction
			Status uint8
		}
		AuditAction.Status = condition.ToUint()
		curDB := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ? AND status = ?", u, condition.ToUint()).Updates(map[string]any{
			"status":        1,
			"audit_opinion": "",
			"audit_time":    "",
			"audit_pass":    0,
			"audit_user_id": 0,
		})
		if curDB.Error != nil {
			return curDB.Error
		}
		if curDB.RowsAffected == 0 {
			return myErrors.NewErr("更新失败")
		}
	}
	//已完成回退
	if condition == action.ActionStatusFinished {
		var file []string
		err := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ?", u).
			Select("photo_preparing,advertise,photo_finished,video,expense_attach,photo,douyin_photo,materials").
			Find(&file).Error
		if err != nil {
			return err
		}
		curDB := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ? AND status = ?", u, condition.ToUint()).Updates(&map[string]any{
			"status":  3,
			"theme":   "",
			"content": "",
			"summary": "",
			"finish":  0,
			//文件字段
			"photo_preparing": "",
			"advertise":       "",
			"photo_finished":  "",
			"video":           "",
			"expense_attach":  "",
			"photo":           "",
			"douyin_photo":    "",
			"materials":       "",
			//保卡信息
			"total":         0,
			"amount":        0,
			"sales_details": "",
		})
		if curDB.Error != nil {
			return curDB.Error
		}
		if curDB.RowsAffected == 0 {
			return myErrors.NewErr("更新失败")
		}
		if len(file) > 0 {
			var urls []string

			//文件格式转换
			for _, v := range file {
				url, err := utils.UrlImageTrans(v)
				if err == nil {
					for _, u := range url {
						urls = append(urls, u)
					}
				} else {
					log.Error("[已完成活动回退]oss文件删除失败")
				}
			}

			//批量删除上传到oss的文件
			err := a.file.DeleteMultiple(urls)
			if err != nil {
				log.Error("[已完成活动回退]oss文件删除失败")
			}
		}
		return nil
	}
	//已核销回退
	if condition == action.ActionStatusWriteOff {
		curDB := a.db.WithContext(c).Model(&model.Actions{}).Where("id = ? AND status = ?", u, condition.ToUint()).Updates(&map[string]any{
			"status":      4,
			"pay_time":    time.Time{},
			"pay_user_id": 0,
			"pay_pass":    0,
			"pay_opinion": "",
		})
		if curDB.Error != nil {
			return curDB.Error
		}
		if curDB.RowsAffected == 0 {
			return myErrors.NewErr("更新失败")
		}
		return nil
	}
	return myErrors.NewErr("错误状态，禁止修改")
}

// GetWarrantyCard 获取活动保卡销售情况
func (a *ActionGorm) GetWarrantyCard(ctx *gin.Context, ActionID uint) (total int, count int, err error) {
	var res struct {
		count int
		total int
	}
	//子查询条件
	subQuery := a.db.WithContext(ctx).Table("actions").
		Select("date_start, date_end,endpoint_id as endpoint").
		Where("id = ?", ActionID)
	//保卡查询
	curDB := a.db.WithContext(ctx).Table("warranty").
		Select("count(id) as count,sum(customer_price) as total").
		Where("buy_date BETWEEN ? AND ?", subQuery.Select("date_start"), subQuery.Select("date_end")).
		Where("status=?", 1).
		Where("endpoint=?", subQuery.Select("endpoint")).
		Group("id")
	return res.total, res.count, curDB.Error
}

// GetActionHadApply 获取终端当月申请某活动总数
func (a *ActionGorm) GetActionHadApply(ctx *gin.Context, endpoint uint, tp int) int {
	var count int
	now := time.Now()
	first := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	end := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
	a.db.WithContext(ctx).Table("actions").
		Select("count(id)").
		Where("endpoint_id = ? AND created_at BETWEEN ? AND ?", endpoint, first, end).
		Where("type=?", tp).
		Find(&count)
	return count
}

// UpdateAction 修改活动信息
func (a *ActionGorm) UpdateAction(ctx *gin.Context, uid uint, actions model.Actions) (int, error) {
	row := a.db.WithContext(ctx).
		Model(&model.Actions{}).
		Where("id = ? and status = ? and uid =?", actions.ID, 1, uid).
		Updates(map[string]any{
			"name":                  actions.Name,
			"level":                 actions.Level,
			"phone":                 actions.Phone,
			"principal":             actions.Principal,
			"plan":                  actions.Plan,
			"staff":                 actions.Staff,
			"site_photo":            actions.SitePhoto,
			"space":                 actions.Space,
			"date_start":            actions.DateStart,
			"date_end":              actions.DateEnd,
			"write_off_corporation": actions.WriteOffCorporation,
			"location":              actions.Location,
			"type":                  actions.Type,
			"updated_at":            actions.UpdateAt,
		})
	if row.RowsAffected == 0 {
		return 0, sql.ErrNoRows
	}
	return int(actions.ID), row.Error
}

// GetActionModel	获取活动信息
func (a *ActionGorm) GetActionModel(c *gin.Context, id int) (model.ActionJoin, error) {
	var act model.ActionJoin
	row := a.db.WithContext(c).
		Table("actions").
		Joins("LEFT JOIN endpoint ON actions.endpoint_id = endpoint.id").
		Joins("LEFT JOIN action_type ON actions.type = action_type.id").
		Select("actions.*, endpoint.name as endpoint_name, action_type.name as type_name").
		Where("actions.id = ?", id).
		Scan(&act)
	if row.RowsAffected == 0 {
		return act, sql.ErrNoRows
	}
	return act, row.Error
}

// GetActionVerifyInfo 获取核验信息
func (a *ActionGorm) GetActionVerifyInfo(c *gin.Context, id, uid int) (act action.VerifyInfoAction, err error) {
	curDB := a.db.WithContext(c).Table("actions").
		Joins("LEFT JOIN admin_users ON actions.audit_user_id = admin_users.id").
		Select("actions.*, admin_users.username as verify_name")
	if uid != 0 {
		curDB = curDB.Where("actions.uid = ?", uid)
	}
	err = curDB.Where("actions.id = ?", id).Scan(&act).Error
	return act, err
}

// GetActionAuditInfo 获取审核信息
func (a *ActionGorm) GetActionAuditInfo(c *gin.Context, id, uid int) (act action.AuditInfoAction, err error) {
	curDB := a.db.WithContext(c).Table("actions").
		Joins("LEFT JOIN admin_users ON actions.audit_user_id = admin_users.id").
		Select("actions.*, admin_users.username as audit_name")
	if uid != 0 {
		curDB = curDB.Where("actions.uid = ?", uid)
	}
	err = curDB.Where("actions.id = ?", id).Scan(&act).Error
	return act, err
}

// GetActionFinishInfo 获取完成信息
func (a *ActionGorm) GetActionFinishInfo(c *gin.Context, id, uid int) (act model.FinishInfoAction, err error) {
	err = a.db.WithContext(c).Table("actions").Where("id = ?", id).Scan(&act).Error
	return act, err
}

// GetActionApplyInfo 获取申请信息
func (a *ActionGorm) GetActionApplyInfo(c *gin.Context, id, uid int) (act model.ApplyInfoActions, err error) {
	curDB := a.db.WithContext(c).
		Table("actions").
		Joins("LEFT JOIN endpoint ON actions.endpoint_id = endpoint.id").
		Joins("LEFT JOIN action_type ON actions.type = action_type.id").
		Select("actions.*, endpoint.name as endpoint_name,action_type.name as type_name,action_type.gift_support")
	if uid != 0 {
		curDB = curDB.Where("actions.uid = ?", uid)
	}
	curDB = curDB.Where("actions.id = ?", id).Scan(&act)
	return act, err
}

func (a *ActionGorm) GetActionStatus(c *gin.Context, id uint) (uint8, error) {
	var status uint8
	a.db.WithContext(c).Table("actions").Select("status").Where("id = ?", id).Scan(&status)
	return status, nil
}

func (a *ActionGorm) DeleteAction(ctx *gin.Context, id int, uid int) error {
	return a.db.WithContext(ctx).
		Where("id = ? and status=? and uid=?", id, 1, uid).
		Delete(&model.Actions{}).Error
}

// SearchActionList 搜索活动列表
func (a *ActionGorm) SearchActionList(ctx *gin.Context, params action.SearchActionParams) ([]action.InfoAction, int64, int, int, error) {
	curDB := a.db.WithContext(ctx).Table("actions")

	// 处理大区筛选
	if params.Partition != 0 && params.TopAgency == 0 {
		var tops []int
		a.db.WithContext(ctx).Table("agency").Select("id").Where("`partition` = ?", params.Partition).Find(&tops)
		curDB = curDB.Where("actions.top_agency_id in (?)", tops)
	}

	// 处理一级代理筛选
	if params.TopAgency != 0 && params.Agency == 0 {
		curDB = curDB.Where("top_agency_id = ?", params.TopAgency)
	}

	// 处理二级代理筛选
	if params.Agency != 0 && params.Endpoint == 0 {
		curDB = curDB.Where("second_agency_id = ?", params.Agency)
	}

	// 处理终端筛选
	if params.Endpoint != 0 {
		curDB = curDB.Where("actions.endpoint_id = ?", params.Endpoint)
	}

	// 处理年份筛选
	if params.Year != "" {
		if params.Year >= "2024" {
			curDB = curDB.Where("actions.application_year = ?", params.Year)
		} else {
			curDB = curDB.Where("YEAR(created_at) = ?", params.Year)
		}
	}

	var startDate, endDate time.Time
	// 处理开始日期
	if params.StartDate != "" {
		startDate, _ = time.Parse("2006-01-02", params.StartDate)
	}
	// 处理结束日期
	if params.EndDate != "" {
		endDate, _ = time.Parse("2006-01-02", params.EndDate)
		// 将结束日期设置为当天的23:59:59，以包含整天
		endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 0, endDate.Location())
	}
	if len(params.IDs) > 0 {
		curDB = curDB.Where("id in (?)", params.IDs)
	}

	switch params.IsNew {
	case "new":
		curDB = curDB.Where("actions.application_year > 0")
	case "old":
		curDB = curDB.Where("actions.application_year = 0")
	}

	// 关联表
	curDB = curDB.Joins("LEFT JOIN action_type ON actions.type=action_type.id").
		Joins("LEFT JOIN endpoint ON actions.endpoint_id = endpoint.id")

	// 处理活动类型和日期筛选
	curDB = curDB.Where("action_type.name like ?", "%"+params.TypeName+"%")

	// 只有当日期参数不为空时才添加日期筛选条件
	if params.StartDate != "" || params.EndDate != "" {
		curDB = curDB.Where("actions.date_start >= ? and actions.date_end <= ?", startDate, endDate)
	}

	// 处理状态筛选
	if params.Status != 0 {
		curDB = curDB.Where("actions.status = ?", params.Status)
	}

	// 处理用户筛选
	if params.UID != 0 {
		curDB = curDB.Where("actions.uid = ?", params.UID)
	}

	// 选择字段并排序
	curDB = curDB.Select("actions.*,endpoint.name as endpoint_name,action_type.name as type_name,actions.application_year as year").
		Order("actions.id desc")

	// 分页处理
	partitions := make([]action.InfoAction, 0)
	var total int64
	curDB.Count(&total)
	curDB.Offset((params.Page - 1) * params.Size).Limit(params.Size).Find(&partitions)

	return partitions, total, params.Page, params.Size, nil
}

func (a *ActionGorm) CreateActivity(c *gin.Context, actions model.Actions) (id int, err error) {
	err = a.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if err = tx.WithContext(c).Create(&actions).Error; err != nil {
			return err
		}
		//生成编号number 格式：20220101123045-1-1
		time := actions.CreateAt
		number := time.Format("20060102150405")
		number = number + "-" + strconv.Itoa(int(actions.UID)) + "-" + strconv.Itoa(int(actions.ID))
		if err = tx.WithContext(c).Table("actions").Where("id = ?", actions.ID).Update("number", number).Error; err != nil {
			return err
		}
		return nil
	})
	return int(actions.ID), err
}

func NewActionGorm(file upload.FileLoadService) ActionDao {
	return &ActionGorm{
		db:   db.GetDB("rbcare"),
		file: file,
	}
}

func NewTestActionGorm(db *gorm.DB) ActionDao {
	return &ActionGorm{db: db}
}
