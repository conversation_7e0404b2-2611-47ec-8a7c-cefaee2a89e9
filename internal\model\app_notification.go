package model

import (
	"marketing/internal/pkg/types"
	"time"
)

// AppNotification 推送消息模型
type AppNotification struct {
	ID        uint                  `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	TypeID    int                   `json:"type_id" gorm:"not null;column:type_id;comment:消息类型ID"`
	Content   string                `json:"content" gorm:"type:text;not null;column:content;comment:消息内容JSON"`
	Platform  types.JSONStringArray `json:"platform" gorm:"type:varchar(100);not null;column:platform;comment:推送平台JSON"`
	Audience  string                `json:"audience" gorm:"type:text;not null;column:audience;comment:推送目标JSON"`
	Revoked   int8                  `json:"revoked" gorm:"type:tinyint(1);not null;default:0;column:revoked;comment:是否撤回"`
	RevokedAt *time.Time            `json:"revoked_at" gorm:"column:revoked_at;comment:撤回时间"`
	CreatedAt time.Time             `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time             `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AppNotification) TableName() string {
	return "app_notification"
}

// AppNotificationInbox 推送消息收件箱模型
type AppNotificationInbox struct {
	ID             uint64    `gorm:"primaryKey;column:id" json:"id"`
	UserID         uint32    `gorm:"not null;column:user_id" json:"user_id"`
	NotificationID uint32    `gorm:"not null;column:notification_id" json:"notification_id"`
	CreatedAt      time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	Fetched        int       `gorm:"not null;default:0;column:fetched" json:"fetched" comment:"是否已经拉取"`
	Read           int       `gorm:"not null;default:0;column:read" json:"read" comment:"消息是否已被浏览"`
	Checked        int       `gorm:"not null;default:0;column:checked" json:"checked" comment:"消息是否已被点击查看"`
}

// TableName 设置表名
func (AppNotificationInbox) TableName() string {
	return "app_notification_inbox"
}

// AppNotificationTag 推送消息标签模型
type AppNotificationTag struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Name      string    `json:"name" gorm:"type:varchar(50);not null;column:name;comment:标签名称"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AppNotificationTag) TableName() string {
	return "app_notification_tag"
}
