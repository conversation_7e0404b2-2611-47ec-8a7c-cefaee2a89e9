package service

import (
	"encoding/json"
	"fmt"
	"marketing/internal/dao"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/pkg/wecom"
	"marketing/internal/service/system"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type PushNotificationSvc interface {
	GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationResp, int64, error)
	GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error)
	GetActiveRoles(c *gin.Context) ([]*RoleResp, error)
	GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error)
	GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error)
	SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error)
	PushNotification(c *gin.Context, params *PushNotificationParams) error
	RevokeNotification(c *gin.Context, id int) error
}

type pushNotificationSvc struct {
	pushDao  dao.PushNotificationDao
	appSvc   system.AppSystemSvc
	userDao  userDao.UserDao
	typeDao  dao.AppNotificationTypeDao
	trainDao dao.TrainDao
}

func NewPushNotificationSvc(pushDao dao.PushNotificationDao, appSvc system.AppSystemSvc,
	userDao userDao.UserDao, typeDao dao.AppNotificationTypeDao, trainDao dao.TrainDao) PushNotificationSvc {
	return &pushNotificationSvc{
		pushDao:  pushDao,
		appSvc:   appSvc,
		userDao:  userDao,
		typeDao:  typeDao,
		trainDao: trainDao,
	}
}

// GetPushNotificationList 获取推送消息列表
func (s *pushNotificationSvc) GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationResp, int64, error) {
	notifications, total, err := s.pushDao.GetPushNotificationList(c, pageNum, pageSize, typeID, content)
	if err != nil {
		return nil, 0, errors.NewErr("获取推送消息列表失败: " + err.Error())
	}

	// 转换为响应格式
	result := make([]*PushNotificationResp, len(notifications))
	for i, notification := range notifications {
		// 解析内容获取标题
		var contentData map[string]interface{}
		contentStr := notification.Content
		if json.Unmarshal([]byte(notification.Content), &contentData) == nil {
			if title, ok := contentData["title"].(string); ok {
				if content, ok := contentData["content"].(string); ok {
					contentStr = title + content
				}
			}
		}

		// 限制内容长度
		if len(contentStr) > 50 {
			contentStr = contentStr[:50] + "..."
		}

		result[i] = &PushNotificationResp{
			ID:       int(notification.ID),
			TypeName: notification.TypeName,
			Content:  contentStr,
			Audience: notification.Audience,
			Total:    int(notification.Total),
			Fetched:  int(notification.Fetched),
			Read:     int(notification.Read),
			Checked:  int(notification.Checked),
			Revoked:  int(notification.Revoked),
		}
	}

	return result, total, nil
}

// GetNotificationTags 获取通知标签
func (s *pushNotificationSvc) GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error) {
	tags, err := s.pushDao.GetWecomTags(c)
	if err != nil {
		return nil, errors.NewErr("获取通知标签失败: " + err.Error())
	}

	result := make([]*NotificationTagResp, len(tags))
	for i, tag := range tags {
		result[i] = &NotificationTagResp{
			ID:   tag.ID,
			Text: tag.TagName,
		}
	}

	return result, nil
}

// GetActiveRoles 获取活跃角色
func (s *pushNotificationSvc) GetActiveRoles(c *gin.Context) ([]*RoleResp, error) {
	roles, err := s.pushDao.GetActiveRoles(c)
	if err != nil {
		return nil, errors.NewErr("获取角色列表失败: " + err.Error())
	}

	result := make([]*RoleResp, len(roles))
	for i, role := range roles {
		result[i] = &RoleResp{
			Slug: role.Slug,
			Name: role.Name,
		}
	}

	return result, nil
}

// GetNewestTrains 获取最新培训资源
func (s *pushNotificationSvc) GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error) {
	// 这里需要实现获取培训资源的逻辑
	// 暂时返回空列表
	result := make([]*TrainResp, 0)
	return result, nil
}

// GetNewestNotices 获取最新通知
func (s *pushNotificationSvc) GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error) {
	notices, err := s.pushDao.GetNewestNotices(c, limit)
	if err != nil {
		return nil, errors.NewErr("获取通知列表失败: " + err.Error())
	}

	result := make([]*NoticeResp, len(notices))
	for i, notice := range notices {
		result[i] = &NoticeResp{
			ID:    notice.ID,
			Title: notice.Title,
		}
	}

	return result, nil
}

// SearchUsers 搜索用户
func (s *pushNotificationSvc) SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error) {
	users, err := s.pushDao.SearchUsers(c, search, page, pageSize)
	if err != nil {
		return nil, errors.NewErr("搜索用户失败: " + err.Error())
	}

	result := make([]*UserResp, len(users))
	for i, user := range users {
		result[i] = &UserResp{
			ID:   user.ID,
			Text: user.Username,
		}
	}

	return result, nil
}

// PushNotification 推送通知
func (s *pushNotificationSvc) PushNotification(c *gin.Context, params *PushNotificationParams) error {
	// 获取推送目标用户
	userIDs, err := s.getNotifyUserIDs(c, params.Audience)
	if err != nil {
		return err
	}

	// 构建企微推送内容
	content := s.buildWecomContent(params)

	// 推送到企业微信
	err = s.pushToWecom(c, userIDs, content, params.Slug, params)

	if err != nil {
		return err
	}

	// 保存推送记录到数据库
	err = s.saveNotificationRecord(c, params, userIDs)
	if err != nil {
		return err
	}

	return nil
}

// RevokeNotification 撤回通知
func (s *pushNotificationSvc) RevokeNotification(c *gin.Context, id int) error {
	notification, err := s.pushDao.GetPushNotificationByID(c, id)
	if err != nil {
		return errors.NewErr("查询消息失败: " + err.Error())
	}
	if notification == nil {
		return errors.NewErr("消息不存在")
	}

	// 检查是否可以撤回（2天内）
	if time.Since(notification.CreatedAt) > 48*time.Hour {
		return errors.NewErr("推送超过两天的消息不能撤回")
	}

	if notification.Revoked == 1 {
		return nil // 已经撤回
	}

	// 更新撤回状态
	if err := s.pushDao.UpdatePushNotificationRevoke(c, id); err != nil {
		return errors.NewErr("撤回消息失败: " + err.Error())
	}

	return nil
}

// getNotifyUserIDs 获取通知用户ID列表
func (s *pushNotificationSvc) getNotifyUserIDs(c *gin.Context, audience map[string]interface{}) ([]uint, error) {
	return s.pushDao.GetUserIDsByAudience(c, audience)
}

// buildWecomContent 构建企微推送内容
func (s *pushNotificationSvc) buildWecomContent(params *PushNotificationParams) string {
	content := fmt.Sprintf("%s\n%s", params.Title, params.Content)

	if params.ActionText != "" && params.URL != "" {
		content += fmt.Sprintf("\n\n%s: %s", params.ActionText, params.URL)
	}

	return content
}

// pushToWecom 推送到企业微信
func (s *pushNotificationSvc) pushToWecom(c *gin.Context, userIDs []uint, content, slug string, params *PushNotificationParams) error {
	if len(userIDs) == 0 {
		return nil
	}
	app, err := s.appSvc.GetAppSystemByKey(c, "app-notice")
	if err != nil {
		return errors.NewErr("获取应用配置失败: " + err.Error())
	}
	wecomClient := wecom.NewWeComClient(app.CorpID, app.CorpSecret)

	// 获取用户的企微ID
	users, err := s.pushDao.GetUsersByIDs(c, userIDs)
	if err != nil {
		return errors.NewErr("获取用户企微ID失败: " + err.Error())
	}

	var qwUserIDs []string
	for _, user := range users {
		if user.QWUserID != nil && *user.QWUserID != "" {
			qwUserIDs = append(qwUserIDs, *user.QWUserID)
		}
	}

	if len(qwUserIDs) == 0 {
		return errors.NewErr("没有找到有效的企微用户ID")
	}

	//====== 推送到企业微信🚀 分批发送，每批最多1000个 ======
	const maxBatch = 1000
	for i := 0; i < len(qwUserIDs); i += maxBatch {
		end := i + maxBatch
		if end > len(qwUserIDs) {
			end = len(qwUserIDs)
		}
		toUser := strings.Join(qwUserIDs[i:end], "|")

		if slug == "notice" {
			// 图文消息
			cover := "https://static.readboy.com/marketing/notice/6884897796025.png"
			articles := []wecom.Article{
				{
					Title:       params.Title,
					Description: params.Content,
					URL:         params.URL,
					PicURL:      cover,
				},
			}
			if _, err := wecomClient.SendNewsMessage(toUser, "", "", app.AgentID, articles); err != nil {
				return errors.NewErr(fmt.Sprintf("推送到企业微信失败 (batch %d-%d): %s", i, end, err.Error()))
			}
		} else {
			if _, err := wecomClient.SendTextCardMessage(
				toUser, "", "", app.AgentID,
				params.Title, content, params.ActionText, params.URL,
			); err != nil {
				return errors.NewErr(fmt.Sprintf("推送到企业微信失败 (batch %d-%d): %s", i, end, err.Error()))
			}
		}
	}

	return nil
}

// saveNotificationRecord 保存通知记录
func (s *pushNotificationSvc) saveNotificationRecord(c *gin.Context, params *PushNotificationParams, userIDs []uint) error {
	// 获取通知类型
	notificationType, err := s.typeDao.GetAppNotificationTypeBySlug(c, params.Slug)
	if err != nil {
		return errors.NewErr("获取通知类型失败: " + err.Error())
	}
	// 构建通知内容JSON
	contentData := map[string]interface{}{
		"group_name":  notificationType.Name,
		"group_icon":  notificationType.Icon,
		"msg_type":    notificationType.MsgType,
		"action_text": notificationType.ActionText,
		"action":      params.Action,
		"content":     params.Content,
		"title":       params.Title,
		"popup":       0,
		"banner":      0,
		"slug":        params.Slug,
	}

	contentJSON, err := json.Marshal(contentData)
	if err != nil {
		return errors.NewErr("序列化通知内容失败: " + err.Error())
	}

	audienceJSON, err := json.Marshal(params.Audience)
	if err != nil {
		return errors.NewErr("序列化推送目标失败: " + err.Error())
	}

	// 保存通知记录
	notification := &model.AppNotification{
		TypeID:    notificationType.ID,
		Content:   string(contentJSON),
		Platform:  params.Platform,
		Audience:  string(audienceJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.pushDao.CreatePushNotification(c, notification); err != nil {
		return errors.NewErr("保存通知记录失败: " + err.Error())
	}

	// 保存用户收件箱记录
	ids, err := utils.GenInboxIds(len(userIDs))
	if err != nil {
		return errors.NewErr("生成收件箱ID失败: " + err.Error())
	}
	var inboxRecords []*model.AppNotificationInbox
	now := time.Now()
	for i, userID := range userIDs {
		inboxRecords = append(inboxRecords, &model.AppNotificationInbox{
			ID:             cast.ToUint64(ids[i]), // 👈 用生成的ID
			UserID:         cast.ToUint32(userID),
			NotificationID: cast.ToUint32(notification.ID),
			CreatedAt:      now,
		})
	}

	if len(inboxRecords) > 0 {
		if err := s.pushDao.CreateNotificationInboxBatch(c, inboxRecords); err != nil {
			return errors.NewErr("保存收件箱记录失败: " + err.Error())
		}
	}

	return nil
}

// PushNotificationParams 推送通知参数
type PushNotificationParams struct {
	Title           string                 `json:"title"`
	Content         string                 `json:"content"`
	Platform        []string               `json:"platform"`
	Audience        map[string]interface{} `json:"audience"`
	Slug            string                 `json:"type_id"`
	Action          string                 `json:"action"`
	ActionText      string                 `json:"action_text"`
	URL             string                 `json:"url"`
	Popup           int                    `json:"popup"`
	Banner          int                    `json:"banner"`
	BannerStartTime string                 `json:"banner_start_time"`
	BannerEndTime   string                 `json:"banner_end_time"`
}

// 响应结构体
type PushNotificationResp struct {
	ID       int      `json:"id"`
	TypeName string   `json:"type_name"`
	Content  string   `json:"content"`
	Platform []string `json:"platform"`
	Audience string   `json:"audience"`
	Total    int      `json:"total"`
	Fetched  int      `json:"fetched"`
	Read     int      `json:"read"`
	Checked  int      `json:"checked"`
	Revoked  int      `json:"revoked"`
}

type NotificationTagResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}

type RoleResp struct {
	Slug string `json:"slug"`
	Name string `json:"name"`
}

type TrainResp struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type NoticeResp struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
}

type UserResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}
